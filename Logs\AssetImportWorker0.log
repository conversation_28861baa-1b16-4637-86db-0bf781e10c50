Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.61f1c1 (327989805ccf) revision 3307913'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.18363) 64bit Enterprise' Language: 'zh' Physical Memory: 16225 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
F:\UnityEditor\2022.3.61f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Project/Unity/Minecraft/Test
-logFile
Logs/AssetImportWorker0.log
-srvPort
49169
Successfully changed project path to: D:/Project/Unity/Minecraft/Test
D:/Project/Unity/Minecraft/Test
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [6124]  Target information:

Player connection [6124]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 1788058488 [EditorId] 1788058488 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-6SADTQQ) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [6124] Host joined multi-casting on [***********:54997]...
Player connection [6124] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
Refreshing native plugins compatible for Editor in 9.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.61f1c1 (327989805ccf)
[Subsystems] Discovering subsystems at path F:/UnityEditor/2022.3.61f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Project/Unity/Minecraft/Test/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1050 Ti (ID=0x1c82)
    Vendor:   NVIDIA
    VRAM:     4004 MB
    Driver:   32.0.15.7680
Initialize mono
Mono path[0] = 'F:/UnityEditor/2022.3.61f1c1/Editor/Data/Managed'
Mono path[1] = 'F:/UnityEditor/2022.3.61f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'F:/UnityEditor/2022.3.61f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56416
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: F:/UnityEditor/2022.3.61f1c1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: F:/UnityEditor/2022.3.61f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004106 seconds.
- Loaded All Assemblies, in  0.362 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.266 seconds
Domain Reload Profiling: 627ms
	BeginReloadAssembly (125ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (145ms)
		LoadAssemblies (124ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (142ms)
			TypeCache.Refresh (140ms)
				TypeCache.ScanAssembly (126ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (266ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (221ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (149ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.778 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Default port 6400 is in use, searching for alternative...
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.Helpers.PortManager:FindAvailablePort () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs:73)
UnityMcpBridge.Editor.Helpers.PortManager:GetPortWithFallback () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs:43)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:109)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs Line: 73)

Found available port 6401
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.Helpers.PortManager:FindAvailablePort () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs:80)
UnityMcpBridge.Editor.Helpers.PortManager:GetPortWithFallback () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs:43)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:109)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs Line: 80)

Saved port 6401 to storage
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.Helpers.PortManager:SavePort (int) (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs:130)
UnityMcpBridge.Editor.Helpers.PortManager:GetPortWithFallback () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs:44)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:109)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs Line: 130)

UnityMcpBridge started on port 6401.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:115)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs Line: 115)

DisplayProgressbar: Generating Metadata
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.151 seconds
Domain Reload Profiling: 2929ms
	BeginReloadAssembly (173ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (511ms)
		LoadAssemblies (400ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (222ms)
			TypeCache.Refresh (186ms)
				TypeCache.ScanAssembly (163ms)
			ScanForSourceGeneratedMonoScriptInfo (25ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (2152ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1944ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (1058ms)
			ProcessInitializeOnLoadMethodAttributes (802ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.08 seconds
Refreshing native plugins compatible for Editor in 4.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4787 Unused Serialized files (Serialized files now loaded: 0)
Unloading 52 unused Assets / (245.9 KB). Loaded Objects now: 5254.
Memory consumption went from 162.5 MB to 162.3 MB.
Total: 9.152900 ms (FindLiveObjects: 0.752000 ms CreateObjectMapping: 0.387800 ms MarkObjects: 7.535800 ms  DeleteObjects: 0.475000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/ManageScene.cs: 1ecd9e5fd308162ff7036c9052824443 -> 49d8940f1575cd95b9827ddb5fd16dc7
  custom:scripting/monoscript/fileName/ManualConfigEditorWindow.cs: 659fa5b77d4bbb5f9b1cf2a3d096fe27 -> 011730f744efb33ce2592548db3b2047
  custom:scripting/monoscript/fileName/ManageGameObject.cs: ab69b1f18894433f83939f376b937a21 -> 25754884c86e3126f771676713d6f699
  custom:scripting/monoscript/fileName/Response.cs: 237b4bd960b507ec2ca7ea979051ae48 -> e709f0eb2188d1fbb1afec36f41d9328
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/ManageShader.cs: bf06695f13ef604ab91996a59a855c5e -> b66cae58f969f2eae0211b350b973414
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/MCPConfigServers.cs: e81cb6a02653c370035298f010b6012e -> 0796f064ea16624a0b516f573884a290
  custom:scripting/monoscript/fileName/McpClients.cs: 8eeb3b6553e90399bbcf70bdcea69628 -> 0e5a751271c9f02301a325d0c658c517
  custom:scripting/monoscript/fileName/GameObjectSerializer.cs: 359abe26e94472eb65611fc468851d3f -> 864cc179fb2c078cb4b00669bf294d46
  custom:scripting/monoscript/fileName/Vector3Helper.cs: c088b3607ac241e423db9709bab83bf3 -> 559e5564704c5fc266a3b31e4fac62ec
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/ServerConfig.cs: f62742c8868a32ce91e50c7987d6b6b9 -> d2a07f714477fbc7d07f1ccf1431a716
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:scripting/monoscript/fileName/UnityMcpEditorWindow.cs: 4818e262a788bd331e82b7e13e5c76d2 -> f33c5e7914ac24e156ec276d33faf089
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/McpClient.cs: ecb6faa299af20e8db469a920bedb27c -> 07e6c0a920fd6dc012106dc5295f8e25
  custom:scripting/monoscript/fileName/ManageScript.cs: 8523e82cb1849ee554ebb45e176ba91b -> 74a082f5bed6ddbfc14dc41a642a5d1f
  custom:scripting/monoscript/fileName/CommandRegistry.cs: 8f3eefd94241ebf8e5ce188a449bec18 -> cdf92ee216427cc8039500b2d9e2f527
  custom:scripting/monoscript/fileName/ManageEditor.cs: 20ae8a4732cfeaed85889a29511764c7 -> 09f28a66ebc87d7e7c38295790ce6531
  custom:scripting/monoscript/fileName/MCPConfigServer.cs: fa451a94f0df15395dd090de11c63305 -> b8033929339949fbfd047edbd5e0a371
  custom:scripting/monoscript/fileName/DefaultServerConfig.cs: 6bb0fd96d2a2878bb32dd0f1bf0eb506 -> 75ec5611aeaf469076c2f53c10588a52
  custom:scripting/monoscript/fileName/ExecuteMenuItem.cs: a8948d9a3949a397b7b37d861d7b6f78 -> 018308c6dcd4d9c9d4029875e9ad9491
  custom:scripting/monoscript/fileName/VSCodeManualSetupWindow.cs: de358bed29e33ed03689850045420b46 -> d62db36733b6e9136904052ae06f9669
  custom:scripting/monoscript/fileName/UnityTypeConverters.cs: 974cc97b285368f38982b49e5ae25b59 -> 94934b374cb945cbc881e849651919eb
  custom:scripting/monoscript/fileName/ReadConsole.cs: 09bb4baf737d05034ba0976f8d401760 -> 3d4b25e80168b0281f0574ffab6a7791
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/ServerInstaller.cs: ad0ab33f11e7e4082f371db4230b985e -> d870f86b808455437c1719a1e598cfea
  custom:scripting/monoscript/fileName/ManageAsset.cs: a75b099a4ef2d2a2cd4d081069f57757 -> 17c5ba6f4bdc08b34d4dd07e03f5fbf2
  custom:scripting/monoscript/fileName/Command.cs: 826ee02fc219a4360036fedbd42b5d2a -> 0b5953c89d9bd84dc5c8a658153510e4
  custom:scripting/monoscript/fileName/PortManager.cs: 1fd4192248d9e504d3bcab4936b929dc -> cfa37760433c81a99652ee8a93609bac
  custom:scripting/monoscript/fileName/UnityMcpBridge.cs: df97d56170d24a603c9de83422a849b7 -> 24d72441c06da78be1b54d3511df913f
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/McpConfig.cs: 47bc5b7dfc40b89535b40f67177ca1b9 -> d5ca0be42b64f3b9c04fa9e044ec961d
========================================================================
Received Import Request.
  Time since last request: 65328.853512 seconds.
  path: Assets/Naninovel/Resources/Naninovel
  artifactKey: Guid(40b16cae8f2cd8a41b3a64d62212bab1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Naninovel/Resources/Naninovel using Guid(40b16cae8f2cd8a41b3a64d62212bab1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'fa8b361e10835dc0fb6dca07891970dd') in 0.003097 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Naninovel/Resources
  artifactKey: Guid(25be1d98caf208d47b1b6fc9527cec2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Naninovel/Resources using Guid(25be1d98caf208d47b1b6fc9527cec2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '61628e896859542fe37977c7f8f05553') in 0.001083 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/NaninovelData
  artifactKey: Guid(8b2e1cb8ea81680468ed28de08399f3a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/NaninovelData using Guid(8b2e1cb8ea81680468ed28de08399f3a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'bab48bb885d9aff49b127cce612feb56') in 0.000691 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Naninovel/Resources/Naninovel/EngineInitializationUI.prefab
  artifactKey: Guid(4c50c16ce50037944a540ca5b289de39) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Naninovel/Resources/Naninovel/EngineInitializationUI.prefab using Guid(4c50c16ce50037944a540ca5b289de39) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'f6945cb5a817eb986be842badf8ea2ed') in 0.057642 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 31
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Naninovel/Resources/Naninovel/ScriptNavigator.prefab
  artifactKey: Guid(735d6aa767879aa469134858c5b2f0a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Naninovel/Resources/Naninovel/ScriptNavigator.prefab using Guid(735d6aa767879aa469134858c5b2f0a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'c1c353a35651a6283a4a0bc4aa7d4a9a') in 0.034997 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 71
========================================================================
Received Import Request.
  Time since last request: 6.369837 seconds.
  path: Assets
  artifactKey: Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets using Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'ae5280ad91af67e2a353905758f96678') in 0.000424 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 25.412110 seconds.
  path: Assets/GameResources
  artifactKey: Guid(c4b8919535a50454f99b0ecb91e516c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameResources using Guid(c4b8919535a50454f99b0ecb91e516c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '61457f4ade59ef7d24839efa57d83c96') in 0.000729 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
2025/8/1 14:26:50|Error|<>c__DisplayClass70_0.<receiveRequest>b__0|A timeout has occurred while reading an HTTP request/response.
2025/8/1 14:26:50|Error|<>c__DisplayClass70_0.<receiveRequest>b__0|A timeout has occurred while reading an HTTP request/response.
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
2025/8/1 14:43:46|Fatal|WebSocketServer.receiveRequest|Thread was being aborted.
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.817 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Default port 6400 is in use, searching for alternative...
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.Helpers.PortManager:FindAvailablePort () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs:73)
UnityMcpBridge.Editor.Helpers.PortManager:GetPortWithFallback () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs:43)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:109)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs Line: 73)

Found available port 6401
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.Helpers.PortManager:FindAvailablePort () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs:80)
UnityMcpBridge.Editor.Helpers.PortManager:GetPortWithFallback () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs:43)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:109)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs Line: 80)

Saved port 6401 to storage
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.Helpers.PortManager:SavePort (int) (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs:130)
UnityMcpBridge.Editor.Helpers.PortManager:GetPortWithFallback () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs:44)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:109)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/Helpers/PortManager.cs Line: 130)

UnityMcpBridge started on port 6401.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:115)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: F:/space/unity-mcp-master/unity-mcp-master/UnityMcpBridge/Editor/UnityMcpBridge.cs Line: 115)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.714 seconds
Domain Reload Profiling: 2532ms
	BeginReloadAssembly (472ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (300ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (280ms)
		LoadAssemblies (328ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (36ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (17ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1714ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1562ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (893ms)
			ProcessInitializeOnLoadMethodAttributes (601ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 5.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 4768 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (216.7 KB). Loaded Objects now: 5266.
Memory consumption went from 159.1 MB to 158.9 MB.
Total: 5.332500 ms (FindLiveObjects: 0.448200 ms CreateObjectMapping: 0.419100 ms MarkObjects: 4.257100 ms  DeleteObjects: 0.207300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:scripting/monoscript/fileName/TypeCacheHelper.cs: c0779edd603459dc3742274167d572b5 -> 
  custom:scripting/monoscript/fileName/UdpSocket.cs: 8c0d397a479fd27af2dbb174e2e93898 -> 7555d065f81d23e6604acb91cf79ddd6
  custom:scripting/monoscript/fileName/Image.cs: 27f7fe2eed0ee693ad26142088b47cca -> f32195bbbc1ca9edcb6c8c6185597e40
  custom:scripting/monoscript/fileName/Cli.cs: be16a0a8bb16d1ef9d2560a778c2da7b -> d287e08b50f2cb135aa4d7f8e01b7ef9
  custom:scripting/monoscript/fileName/KnownAssemblies.cs: eeea5c54de119292c9e35e827e39077b -> 286bea9f8fcee5e392f6fc0f0cd541c8
  custom:scripting/monoscript/fileName/SimpleJSON.cs: a9256822d1ed79303dff4bf4222a8161 -> 70c028a07c79c166db015acda1282d86
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:scripting/monoscript/fileName/SolutionProjectEntry.cs: ad134947b839971a55dcc89fa7def608 -> 68cea09e9af09443edf61e18806ac1c2
  custom:scripting/monoscript/fileName/ManageScene.cs: 1ecd9e5fd308162ff7036c9052824443 -> 49d8940f1575cd95b9827ddb5fd16dc7
  custom:scripting/monoscript/fileName/TcpListener.cs: 2e837178dd3ee07b0dd871ea47f79d1c -> 940bfdac0e91ad2884e6359468f2782a
  custom:scripting/monoscript/fileName/TcpClient.cs: 62f63a757b531974565dd3360d33c3e2 -> a949cfd4c46427829913ee0195fa0103
  custom:scripting/monoscript/fileName/ManualConfigEditorWindow.cs: 659fa5b77d4bbb5f9b1cf2a3d096fe27 -> 011730f744efb33ce2592548db3b2047
  custom:scripting/monoscript/fileName/ManageGameObject.cs: ab69b1f18894433f83939f376b937a21 -> 25754884c86e3126f771676713d6f699
  custom:scripting/monoscript/fileName/LegacyStyleProjectGeneration.cs: 6efb2c7b459d2c5ffe8dc0ca415a06d9 -> 4858a0ab74312ccaa3ff27a510a9d575
  custom:scripting/monoscript/fileName/FileUtility.cs: 71f27212df47cad3593aaf64e7d40726 -> 2d70b80c72be1fb3b2b89ce0939e8cef
  custom:scripting/monoscript/fileName/Response.cs: 237b4bd960b507ec2ca7ea979051ae48 -> e709f0eb2188d1fbb1afec36f41d9328
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:scripting/monoscript/fileName/ManageShader.cs: bf06695f13ef604ab91996a59a855c5e -> b66cae58f969f2eae0211b350b973414
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/MCPConfigServers.cs: e81cb6a02653c370035298f010b6012e -> 0796f064ea16624a0b516f573884a290
  custom:scripting/monoscript/fileName/Messenger.cs: e5d3a13331933d43889756e46bbd2d7d -> f4ebc46d09b2b4de489946914db2aae0
  custom:scripting/monoscript/fileName/McpClients.cs: 8eeb3b6553e90399bbcf70bdcea69628 -> 0e5a751271c9f02301a325d0c658c517
  custom:scripting/monoscript/fileName/ProcessRunner.cs: 091011eda0e59a4b13abcb3f2a437c71 -> 932a2a80cb9a3539199f645deff3c585
  custom:scripting/monoscript/fileName/GameObjectSerializer.cs: 359abe26e94472eb65611fc468851d3f -> 864cc179fb2c078cb4b00669bf294d46
  custom:scripting/monoscript/fileName/GeneratorFactory.cs: e95f2d8d99ff452f8f55fc3dbdf8b3b5 -> 
  custom:scripting/monoscript/fileName/ExceptionEventArgs.cs: e02709200f0b16494a1611966a13889c -> 27cb4c00a4137b184e01164606c88685
  custom:scripting/monoscript/fileName/Vector3Helper.cs: c088b3607ac241e423db9709bab83bf3 -> 559e5564704c5fc266a3b31e4fac62ec
  custom:scripting/monoscript/fileName/ProjectGeneration.cs: 817c38bb47e1c6c631a52ee70c3a29a4 -> ad99001a96dac276206e089b138a3695
  custom:scripting/monoscript/fileName/Symbols.cs: 45ec383a13e5ac18bbf85f25db5b131e -> 27613575e8bda8a6448f80ff27c488f9
  custom:scripting/monoscript/fileName/VersionPair.cs: 83d1d4ad8ea7a98456de1bf0e51c2809 -> 88b4a0b7e953537f26ded0a68cf4c238
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:scripting/monoscript/fileName/TestResultAdaptor.cs: 16f5b03abe348a2daca79e8d44a65466 -> fb4db3971d5379750cda884e7e44e3e4
  custom:scripting/monoscript/fileName/VisualStudioEditor.cs: 0723bb91171820644eb6c92edfe7a716 -> 21e59f688ad500ab61aca318850ec505
  custom:scripting/monoscript/fileName/VisualStudioInstallation.cs: 0d67d8617dccb9ec1e3d5b40231d5c9c -> 41612f13db7783c8635801ab159c1079
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/ServerConfig.cs: f62742c8868a32ce91e50c7987d6b6b9 -> d2a07f714477fbc7d07f1ccf1431a716
  custom:scripting/monoscript/fileName/Solution.cs: 0ce897859916f31a23b527b10b82ea1c -> 8bc97d0abd509f39b67b3a5bbac1b9a1
  custom:scripting/monoscript/fileName/GUIDProvider.cs: 42851dcbdcf2b3e052b42a23bdc10c5e -> cdd3f47e4fe593d2dfdf9915fa537e3f
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:scripting/monoscript/fileName/UnityMcpEditorWindow.cs: 4818e262a788bd331e82b7e13e5c76d2 -> f33c5e7914ac24e156ec276d33faf089
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/MessageEventArgs.cs: ff5ca0eb4b435cf92aa845f4d510328b -> 528473bbcee3623fe954627c6bfadd2e
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:scripting/monoscript/fileName/ProjectProperties.cs: b180a07572fd00ffb8a37c98ca6e2b49 -> 407063898a13eaad2cd100e43551c6b5
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/McpClient.cs: ecb6faa299af20e8db469a920bedb27c -> 07e6c0a920fd6dc012106dc5295f8e25
  custom:scripting/monoscript/fileName/UnityInstallation.cs: b5b03b3b3e06c1a279a8d0206f524a8c -> 0ba30d4545f2c34a9323456723b5dbfe
  custom:scripting/monoscript/fileName/ManageScript.cs: 8523e82cb1849ee554ebb45e176ba91b -> 74a082f5bed6ddbfc14dc41a642a5d1f
  custom:scripting/monoscript/fileName/VisualStudioForWindowsInstallation.cs: e70c2e2763bdde7021628a1ae198990a -> 468ed6fb57813d3de23aa53f5f59c2eb
  custom:scripting/monoscript/fileName/CommandRegistry.cs: 8f3eefd94241ebf8e5ce188a449bec18 -> cdf92ee216427cc8039500b2d9e2f527
  custom:scripting/monoscript/fileName/SolutionParser.cs: d75126b0174a0c9995bacb91f692ba43 -> 240a75b5fc2682997268aa77e6a1dd86
  custom:scripting/monoscript/fileName/TestAdaptor.cs: 4286d6ce850b7e57b6ad1d9b342a9979 -> 486cec8559bad7c56adc699f165feb67
  custom:scripting/monoscript/fileName/ManageEditor.cs: 20ae8a4732cfeaed85889a29511764c7 -> 09f28a66ebc87d7e7c38295790ce6531
  custom:scripting/monoscript/fileName/Discovery.cs: 4a714a23d67326a1967947001a9c5fa9 -> 2b1006b9be47307c73378eaafc98b6fb
  custom:scripting/monoscript/fileName/MCPConfigServer.cs: fa451a94f0df15395dd090de11c63305 -> b8033929339949fbfd047edbd5e0a371
  custom:scripting/monoscript/fileName/DefaultServerConfig.cs: 6bb0fd96d2a2878bb32dd0f1bf0eb506 -> 75ec5611aeaf469076c2f53c10588a52
  custom:scripting/monoscript/fileName/ExecuteMenuItem.cs: a8948d9a3949a397b7b37d861d7b6f78 -> 018308c6dcd4d9c9d4029875e9ad9491
  custom:scripting/monoscript/fileName/VisualStudioIntegration.cs: cfa3622c505da8be2039caefbb88d122 -> f9e623c49f9f20ea97a4519aa48d72d4
  custom:scripting/monoscript/fileName/VSCodeManualSetupWindow.cs: de358bed29e33ed03689850045420b46 -> d62db36733b6e9136904052ae06f9669
  custom:scripting/monoscript/fileName/FileIOProvider.cs: 1ce0476a672b5b71a81d6ee006010383 -> 304525d180d2b661ce338fc8a3225752
  custom:scripting/monoscript/fileName/SdkStyleProjectGeneration.cs: 1312a090da7ddeca3f762f374848d08f -> bbd8a39d16aad1a0b8d5240b1f43f4a4
  custom:scripting/monoscript/fileName/UnityTypeConverters.cs: 974cc97b285368f38982b49e5ae25b59 -> 94934b374cb945cbc881e849651919eb
  custom:scripting/monoscript/fileName/ReadConsole.cs: 09bb4baf737d05034ba0976f8d401760 -> 3d4b25e80168b0281f0574ffab6a7791
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/SolutionProperties.cs: e56a3118082c5b341d1dc42cac5ce89b -> ab8751e49ae2f3d9994444a0a38619bd
  custom:scripting/monoscript/fileName/ServerInstaller.cs: ad0ab33f11e7e4082f371db4230b985e -> d870f86b808455437c1719a1e598cfea
  custom:scripting/monoscript/fileName/TestRunnerApiListener.cs: 6c187faf0bd0f433a681c31e7512bd7e -> cc17cec8825a8f933d6ecbd68d51f1de
  custom:scripting/monoscript/fileName/AssemblyNameProvider.cs: 412dd6a1178a8d30db9697e3966d84a7 -> 5210529ddc67cdb852122f447527c528
  custom:scripting/monoscript/fileName/ManageAsset.cs: a75b099a4ef2d2a2cd4d081069f57757 -> 17c5ba6f4bdc08b34d4dd07e03f5fbf2
  custom:scripting/monoscript/fileName/Serializer.cs: 93add3d414874bb21069d5380b7e3260 -> d7df1b97d0a3bd0bb98fd4d2d5bf5d29
  custom:scripting/monoscript/fileName/Command.cs: 826ee02fc219a4360036fedbd42b5d2a -> 0b5953c89d9bd84dc5c8a658153510e4
  custom:scripting/monoscript/fileName/PortManager.cs: 1fd4192248d9e504d3bcab4936b929dc -> cfa37760433c81a99652ee8a93609bac
  custom:scripting/monoscript/fileName/Message.cs: dae271262c0220d8340d0a3b5a902790 -> 671a789acb2c7a93d0b620fe02979957
  custom:scripting/monoscript/fileName/Deserializer.cs: 5a795d9395ad7e35e14ed6e294af204a -> 1cc72e9ed2428cc6d6ce2d4d2ad7b02b
  custom:scripting/monoscript/fileName/UnityMcpBridge.cs: df97d56170d24a603c9de83422a849b7 -> 24d72441c06da78be1b54d3511df913f
  custom:scripting/precompiled-assembly-types:Unity.VisualStudio.Editor: 29dbd6c3068c4562a9a94f5176001be7 -> 8196c7ff3a716df6975b114b464f1975
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/TestRunnerCallbacks.cs: 3af5d6a10f138e57c289f4f7d34ae28c -> 5627cb9f4ed769fba607b0cab8e9e63d
  custom:scripting/monoscript/fileName/AsyncOperation.cs: 8f6297df34271fdb1ce2d9607ec6261d -> 90647cc400823b41df847a6cbfc29026
  custom:scripting/monoscript/fileName/McpConfig.cs: 47bc5b7dfc40b89535b40f67177ca1b9 -> d5ca0be42b64f3b9c04fa9e044ec961d
  custom:scripting/monoscript/fileName/UsageUtility.cs: 8b1af87d43264a213340a26d31d1dfd4 -> 5ed3d13206b2b96245ce59a5c83beed1
  custom:scripting/monoscript/fileName/VisualStudioCodeInstallation.cs: 6f0a3e0ea4f0d945a12c678fba9cd672 -> 6b6ec59c4ff0bddfc5db8b3c453d5172
========================================================================
Received Import Request.
  Time since last request: 1120.542087 seconds.
  path: Assets/GameResources/Backgrounds
  artifactKey: Guid(a9d93b0cfc6e3e340867745031ac5189) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameResources/Backgrounds using Guid(a9d93b0cfc6e3e340867745031ac5189) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'e06520f9e0e6935eb9e542cef494456c') in 0.001462 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
2025/8/1 14:45:19|Error|<>c__DisplayClass70_0.<receiveRequest>b__0|A timeout has occurred while reading an HTTP request/response.
2025/8/1 14:45:19|Error|<>c__DisplayClass70_0.<receiveRequest>b__0|A timeout has occurred while reading an HTTP request/response.
